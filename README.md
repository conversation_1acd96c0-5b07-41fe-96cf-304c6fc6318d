# Job Search Assistant

A comprehensive Flask-based web application for job searching that helps you find company domains, HR contacts, generate email patterns, validate emails, and send templated emails.

## Features

### 🏢 Company Domain Search
- Search for company domains based on company names
- Automatic domain discovery using multiple methods
- Company information storage

### 👥 HR Contact Discovery
- Find HR and Talent Acquisition professionals
- LinkedIn profile integration
- Contact information management

### 📧 Email Generation & Validation
- Generate multiple email patterns for each contact
- Email validation using DNS and SMTP checks
- Support for various email formats (<EMAIL>, etc.)

### 📬 Email Management
- Dynamic SMTP configuration setup
- Templated email creation with personalization
- Bulk email sending capabilities
- Email delivery tracking

## Installation

1. Clone the repository or download the files
2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configurations
```

4. Run the application:
```bash
python app.py
```

5. Open your browser and navigate to `http://localhost:5000`

## Usage

### Tab 1: Company & Contact Search

1. **Company Search**: Enter a company name to find their domain and website
2. **Contact Search**: After finding a company, search for HR professionals
3. **Email Generation**: View generated email patterns for each contact
4. **Email Validation**: Validate email addresses before sending

### Tab 2: Email Management

1. **SMTP Configuration**: Set up your email server settings
2. **Email Templates**: Create personalized email templates
3. **Send Emails**: Select recipients and send templated emails

## SMTP Configuration Examples

### Gmail
- SMTP Server: `smtp.gmail.com`
- Port: `587`
- Use TLS: ✓
- Email: Your Gmail address
- Password: App-specific password (not your regular password)

### Outlook/Hotmail
- SMTP Server: `smtp-mail.outlook.com`
- Port: `587`
- Use TLS: ✓
- Email: Your Outlook address
- Password: Your Outlook password

### Yahoo
- SMTP Server: `smtp.mail.yahoo.com`
- Port: `587`
- Use TLS: ✓
- Email: Your Yahoo address
- Password: App-specific password

## Email Template Variables

Use these variables in your email templates for personalization:
- `{name}`: Recipient's name

Example template:
```
Subject: Job Opportunity - {name}

Dear {name},

I hope this email finds you well. I am reaching out regarding potential job opportunities at your company...

Best regards,
[Your Name]
```

## Database

The application uses SQLite database with the following tables:
- `Company`: Stores company information
- `Contact`: Stores contact details
- `EmailTemplate`: Stores email templates
- `SMTPConfig`: Stores SMTP configurations

## Security Notes

- Never commit your `.env` file with real credentials
- Use app-specific passwords for email providers
- Consider using environment variables for production deployment
- Implement rate limiting for email sending in production

## Limitations

- Email validation may not be 100% accurate due to server restrictions
- Some email providers may block automated emails
- Google search scraping may be rate-limited
- LinkedIn contact search is currently simulated (requires API integration for real data)

## Production Deployment

For production deployment:
1. Use a production WSGI server (gunicorn, uWSGI)
2. Set up proper environment variables
3. Use a production database (PostgreSQL, MySQL)
4. Implement proper logging
5. Add rate limiting
6. Use HTTPS

## Contributing

Feel free to submit issues and enhancement requests!

## License

This project is open source and available under the MIT License.