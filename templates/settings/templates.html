{% extends "base.html" %}

{% block title %}Email Templates - JobSearch Pro{% endblock %}

{% block content %}
<div class="container mt-5 pt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('settings') }}">Settings</a></li>
                    <li class="breadcrumb-item active">Email Templates</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="modern-card">
                <div class="card-header-modern">
                    <div class="card-icon bg-warning">
                        <i class="bi bi-file-earmark-text"></i>
                    </div>
                    <h4 class="mb-0">Create Email Template</h4>
                </div>
                <div class="card-body-modern">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="form-floating mb-3">
                            {{ form.name(class="form-control") }}
                            {{ form.name.label(class="form-label") }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.name.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-floating mb-3">
                            {{ form.subject(class="form-control") }}
                            {{ form.subject.label(class="form-label") }}
                            {% if form.subject.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.subject.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.body.id }}" class="form-label">Email Body</label>
                            {{ form.body(class="form-control", style="height: 300px;") }}
                            {% if form.body.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.body.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid">
                            {{ form.submit(class="btn btn-warning btn-lg") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="modern-card">
                <div class="card-header-modern">
                    <div class="card-icon bg-info">
                        <i class="bi bi-lightbulb"></i>
                    </div>
                    <h5 class="mb-0">Template Variables</h5>
                </div>
                <div class="card-body-modern">
                    <p class="small mb-3">You can use these variables in your email template:</p>
                    
                    <div class="mb-3">
                        <h6 class="text-primary">Available Variables:</h6>
                        <ul class="list-unstyled small">
                            <li class="mb-2"><code>{name}</code> - Recipient's name</li>
                            <li class="mb-2"><code>{company_name}</code> - Company name</li>
                            <li class="mb-2"><code>{title}</code> - Recipient's job title</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <small><strong>Tip:</strong> Use HTML formatting for better email appearance. The template supports basic HTML tags.</small>
                    </div>
                    
                    <div class="mt-4">
                        <h6 class="text-primary">Sample Template:</h6>
                        <div class="bg-light p-3 rounded small">
                            <code>
                                Dear {name},<br><br>
                                I hope this email finds you well. I am writing to express my interest in potential opportunities at {company_name}.<br><br>
                                As a {title}, I believe you would be the right person to discuss how my skills could contribute to your team.<br><br>
                                Best regards,<br>
                                [Your Name]
                            </code>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Existing Templates -->
    {% if templates %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="modern-card">
                <div class="card-header-modern">
                    <div class="card-icon bg-success">
                        <i class="bi bi-list"></i>
                    </div>
                    <h5 class="mb-0">Your Email Templates</h5>
                </div>
                <div class="card-body-modern">
                    <div class="row g-4">
                        {% for template in templates %}
                        <div class="col-md-6">
                            <div class="card border">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        {{ template.name }}
                                        {% if template.is_default %}
                                            <span class="badge bg-primary ms-2">Default</span>
                                        {% endif %}
                                    </h6>
                                    <form method="POST" action="{{ url_for('delete_email_template', template_id=template.id) }}" class="d-inline">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                        <button type="submit" class="btn btn-outline-danger btn-sm"
                                                onclick="return confirm('Are you sure you want to delete this template?')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </div>
                                <div class="card-body">
                                    <p class="card-text"><strong>Subject:</strong> {{ template.subject }}</p>
                                    <div class="card-text">
                                        <strong>Preview:</strong>
                                        <div class="bg-light p-2 rounded small" style="max-height: 150px; overflow-y: auto;">
                                            {{ template.body[:200] }}{% if template.body|length > 200 %}...{% endif %}
                                        </div>
                                    </div>
                                    <small class="text-muted">Created: {{ template.created_at.strftime('%B %d, %Y') }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}