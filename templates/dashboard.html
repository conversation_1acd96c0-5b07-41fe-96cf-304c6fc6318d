{% extends "base.html" %}

{% block title %}Dashboard - JobSearch Pro{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="hero-content">
                    <h1 class="hero-title">Welcome back, {{ current_user.username }}!</h1>
                    <p class="hero-description">
                        Ready to discover more HR contacts and land your dream job? Let's continue your job search journey.
                    </p>
                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number">{{ recent_companies|length }}</div>
                            <div class="stat-label">Companies Searched</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ recent_contacts|length }}</div>
                            <div class="stat-label">Contacts Found</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ smtp_configs|length }}</div>
                            <div class="stat-label">SMTP Configs</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ email_templates|length }}</div>
                            <div class="stat-label">Email Templates</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="hero-illustration text-center">
                    <div class="floating-card bg-white shadow-lg rounded-4 p-4 mb-4">
                        <div class="d-flex align-items-center">
                            <div class="avatar bg-primary bg-gradient rounded-circle me-3 d-flex align-items-center justify-content-center">
                                <i class="bi bi-person text-white"></i>
                            </div>
                            <div class="text-start">
                                <div class="fw-semibold">{{ current_user.username }}</div>
                                <div class="text-muted small">{{ current_user.email }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-5">
    <!-- Quick Actions -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="quick-actions-card">
                <h3 class="text-center mb-4">Start Your Job Search Journey</h3>
                <div class="row g-4">
                    <div class="col-md-4">
                        <div class="quick-action-item" data-action="search">
                            <div class="qa-icon">
                                <i class="bi bi-building"></i>
                            </div>
                            <h5>Find Company</h5>
                            <p>Search for company information and discover their domain</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="quick-action-item" data-action="contacts">
                            <div class="qa-icon">
                                <i class="bi bi-people"></i>
                            </div>
                            <h5>Discover HR Contacts</h5>
                            <p>Find HR professionals and talent acquisition experts</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="quick-action-item" data-action="email">
                            <div class="qa-icon">
                                <i class="bi bi-send"></i>
                            </div>
                            <h5>Send Outreach</h5>
                            <p>Create and send personalized emails to HR contacts</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Pills -->
    <ul class="nav nav-pills nav-custom justify-content-center mb-4" id="mainTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="search-tab" data-bs-toggle="pill" data-bs-target="#search" type="button" role="tab">
                <i class="bi bi-search me-2"></i>Company & Contacts
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="email-tab" data-bs-toggle="pill" data-bs-target="#email" type="button" role="tab">
                <i class="bi bi-envelope me-2"></i>Email Management
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="bulk-tab" data-bs-toggle="pill" data-bs-target="#bulk" type="button" role="tab">
                <i class="bi bi-lightning me-2"></i>Bulk Operations
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="history-tab" data-bs-toggle="pill" data-bs-target="#history" type="button" role="tab">
                <i class="bi bi-clock-history me-2"></i>History
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="mainTabContent">
        <!-- Search Tab -->
        <div class="tab-pane fade show active" id="search" role="tabpanel">
            <div class="row g-4">
                <!-- Company Search -->
                <div class="col-lg-6">
                    <div class="modern-card">
                        <div class="card-header-modern">
                            <div class="card-icon bg-primary">
                                <i class="bi bi-building"></i>
                            </div>
                            <h5 class="mb-0">Company Discovery</h5>
                        </div>
                        <div class="card-body-modern">
                            <form id="companySearchForm">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="companyName" placeholder="Enter company name" required>
                                    <label for="companyName">Company Name</label>
                                </div>
                                <button type="submit" class="btn btn-primary btn-lg w-100">
                                    <span class="loading d-none spinner-border spinner-border-sm me-2"></span>
                                    <i class="bi bi-search me-2"></i>
                                    Search Company
                                </button>
                            </form>
                            
                            <div id="companyResults" class="result-section mt-4" style="display: none;">
                                <div class="result-header">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Company Found
                                </div>
                                <div id="companyInfo"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Search -->
                <div class="col-lg-6">
                    <div class="modern-card">
                        <div class="card-header-modern">
                            <div class="card-icon bg-success">
                                <i class="bi bi-people"></i>
                            </div>
                            <h5 class="mb-0">HR Contacts</h5>
                        </div>
                        <div class="card-body-modern">
                            <button type="button" class="btn btn-success btn-lg w-100" id="searchContactsBtn" disabled>
                                <span class="loading d-none spinner-border spinner-border-sm me-2"></span>
                                <i class="bi bi-people-fill me-2"></i>
                                Find HR Contacts
                            </button>
                            <p class="help-text mt-3">
                                <i class="bi bi-info-circle me-1"></i>
                                First search for a company to enable contact discovery
                            </p>
                            
                            <div id="contactResults" class="result-section mt-4" style="display: none;">
                                <div class="result-header">
                                    <i class="bi bi-person-check text-success me-2"></i>
                                    HR Contacts Found
                                </div>
                                <div id="contactsList"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Email Generation Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="modern-card" id="emailValidationSection" style="display: none;">
                        <div class="card-header-modern">
                            <div class="card-icon bg-warning">
                                <i class="bi bi-at"></i>
                            </div>
                            <h5 class="mb-0">Email Generation & Validation</h5>
                        </div>
                        <div class="card-body-modern">
                            <div id="generatedEmails"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Email Management Tab -->
        <div class="tab-pane fade" id="email" role="tabpanel">
            <div class="row g-4">
                <!-- SMTP Configuration -->
                <div class="col-lg-6">
                    <div class="modern-card">
                        <div class="card-header-modern">
                            <div class="card-icon bg-info">
                                <i class="bi bi-gear"></i>
                            </div>
                            <h5 class="mb-0">SMTP Configuration</h5>
                        </div>
                        <div class="card-body-modern">
                            {% if smtp_configs %}
                                <div class="mb-3">
                                    <label class="form-label">Select SMTP Configuration:</label>
                                    <select class="form-select" id="smtpConfigSelect">
                                        <option value="">Select configuration...</option>
                                        {% for config in smtp_configs %}
                                            <option value="{{ config.id }}" {% if config.is_default %}selected{% endif %}>
                                                {{ config.name }} ({{ config.email }})
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    No SMTP configurations found. <a href="{{ url_for('smtp_settings') }}">Add one now</a>.
                                </div>
                            {% endif %}

                            <a href="{{ url_for('smtp_settings') }}" class="btn btn-info btn-lg w-100">
                                <i class="bi bi-plus-circle me-2"></i>
                                Manage SMTP Configs
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Email Templates -->
                <div class="col-lg-6">
                    <div class="modern-card">
                        <div class="card-header-modern">
                            <div class="card-icon bg-warning">
                                <i class="bi bi-file-earmark-text"></i>
                            </div>
                            <h5 class="mb-0">Email Templates</h5>
                        </div>
                        <div class="card-body-modern">
                            {% if email_templates %}
                                <div class="mb-3">
                                    <label class="form-label">Select Email Template:</label>
                                    <select class="form-select" id="emailTemplateSelect">
                                        <option value="">Select template...</option>
                                        {% for template in email_templates %}
                                            <option value="{{ template.id }}" {% if template.is_default %}selected{% endif %}>
                                                {{ template.name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    No email templates found. <a href="{{ url_for('template_settings') }}">Add one now</a>.
                                </div>
                            {% endif %}

                            <a href="{{ url_for('template_settings') }}" class="btn btn-warning btn-lg w-100 mb-2">
                                <i class="bi bi-plus-circle me-2"></i>
                                Manage Templates
                            </a>
                            <a href="{{ url_for('manual_email_test') }}" class="btn btn-outline-success btn-lg w-100">
                                <i class="bi bi-bug me-2"></i>
                                Test Manual Email
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Send Outreach Emails Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="modern-card">
                        <div class="card-header-modern">
                            <div class="card-icon bg-success">
                                <i class="bi bi-send"></i>
                            </div>
                            <h5 class="mb-0">Send Outreach Emails</h5>
                        </div>
                        <div class="card-body-modern">
                            <!-- Email Input Options -->
                            <div class="email-input-options mb-4">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="input-option-card" id="foundContactsOption">
                                            <div class="option-header">
                                                <i class="bi bi-people-fill text-primary"></i>
                                                <h6 class="mb-0">Use Found Contacts</h6>
                                            </div>
                                            <p class="text-muted small mb-3">Send emails to contacts discovered through company search</p>
                                            <div id="selectedRecipients" class="recipients-list">
                                                <div class="empty-state">
                                                    <i class="bi bi-inbox"></i>
                                                    <p>No recipients selected</p>
                                                    <small>Go to Company & Contacts tab to find contacts</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-option-card" id="manualEmailOption">
                                            <div class="option-header">
                                                <i class="bi bi-pencil-square text-success"></i>
                                                <h6 class="mb-0">Manual Email Entry</h6>
                                            </div>
                                            <p class="text-muted small mb-3">Enter email addresses manually</p>
                                            <div class="manual-email-input">
                                                <div class="form-floating mb-3">
                                                    <input type="email" class="form-control" id="manualEmailAddress" placeholder="Enter email address">
                                                    <label for="manualEmailAddress">Email Address</label>
                                                </div>
                                                <div class="form-floating mb-3">
                                                    <input type="text" class="form-control" id="manualContactName" placeholder="Enter contact name">
                                                    <label for="manualContactName">Contact Name (Optional)</label>
                                                </div>
                                                <div class="d-grid gap-2">
                                                    <button type="button" class="btn btn-outline-success btn-sm" id="addManualEmailBtn">
                                                        <i class="bi bi-plus-circle me-1"></i>Add Email
                                                    </button>
                                                    <button type="button" class="btn btn-outline-info btn-sm" id="testManualEmailBtn">
                                                        <i class="bi bi-bug me-1"></i>Test Function
                                                    </button>
                                                </div>
                                            </div>
                                            <div id="manualEmailList" class="manual-email-list mt-3"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Email Configuration -->
                            <div class="email-config-section">
                                <div class="row g-3 mb-4">
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="sendSmtpConfig">
                                                <option value="">Select SMTP Configuration</option>
                                                {% for config in smtp_configs %}
                                                    <option value="{{ config.id }}" {% if config.is_default %}selected{% endif %}>
                                                        {{ config.name }} ({{ config.email }})
                                                    </option>
                                                {% endfor %}
                                            </select>
                                            <label>SMTP Configuration</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <select class="form-select" id="sendEmailTemplate">
                                                <option value="">Select Email Template</option>
                                                {% for template in email_templates %}
                                                    <option value="{{ template.id }}" {% if template.is_default %}selected{% endif %}>
                                                        {{ template.name }}
                                                    </option>
                                                {% endfor %}
                                            </select>
                                            <label>Email Template</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Send Button -->
                                <button type="button" class="btn btn-success btn-lg w-100" id="sendEmailsBtn">
                                    <span class="loading d-none spinner-border spinner-border-sm me-2"></span>
                                    <i class="bi bi-send-fill me-2"></i>
                                    Send Outreach Emails
                                </button>
                            </div>

                            <!-- Email Send Results -->
                            <div id="emailSendResults" class="result-section mt-4" style="display: none;">
                                <div class="result-header">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Email Send Results
                                </div>
                                <div id="sendResultsList"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Bulk Operations Tab -->
        <div class="tab-pane fade" id="bulk" role="tabpanel">
            <div class="modern-card">
                <div class="card-header-modern">
                    <div class="card-icon bg-danger">
                        <i class="bi bi-lightning"></i>
                    </div>
                    <h5 class="mb-0">Bulk Search & Email</h5>
                </div>
                <div class="card-body-modern">
                    <form id="bulkOperationForm">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="bulkCompanyName" placeholder="Enter company name" required>
                                    <label for="bulkCompanyName">Company Name</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating">
                                    <select class="form-select" id="bulkSmtpConfig">
                                        <option value="">Select SMTP...</option>
                                        {% for config in smtp_configs %}
                                            <option value="{{ config.id }}">{{ config.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <label>SMTP Configuration</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating">
                                    <select class="form-select" id="bulkEmailTemplate">
                                        <option value="">Select Template...</option>
                                        {% for template in email_templates %}
                                            <option value="{{ template.id }}">{{ template.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <label>Email Template</label>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-danger btn-lg w-100 mt-4">
                            <span class="loading d-none spinner-border spinner-border-sm me-2"></span>
                            <i class="bi bi-lightning-fill me-2"></i>
                            Search & Send Bulk Emails
                        </button>
                    </form>
                    
                    <div id="bulkResults" class="result-section mt-4" style="display: none;">
                        <div class="result-header">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            Bulk Operation Results
                        </div>
                        <div id="bulkResultsContent"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- History Tab -->
        <div class="tab-pane fade" id="history" role="tabpanel">
            <div class="row g-4">
                <!-- Recent Companies -->
                {% if recent_companies %}
                <div class="col-lg-6">
                    <div class="modern-card">
                        <div class="card-header-modern">
                            <div class="card-icon bg-primary">
                                <i class="bi bi-building"></i>
                            </div>
                            <h5 class="mb-0">Recent Companies</h5>
                        </div>
                        <div class="card-body-modern">
                            <div class="list-group list-group-flush">
                                {% for company in recent_companies %}
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">{{ company.name }}</h6>
                                        <small class="text-muted">
                                            {% if company.domain %}{{ company.domain }}{% else %}Domain not found{% endif %}
                                        </small>
                                    </div>
                                    <small class="text-muted">{{ company.created_at.strftime('%m/%d/%Y') }}</small>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Recent Contacts -->
                {% if recent_contacts %}
                <div class="col-lg-6">
                    <div class="modern-card">
                        <div class="card-header-modern">
                            <div class="card-icon bg-success">
                                <i class="bi bi-people"></i>
                            </div>
                            <h5 class="mb-0">Recent Contacts</h5>
                        </div>
                        <div class="card-body-modern">
                            <div class="list-group list-group-flush">
                                {% for contact in recent_contacts %}
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">{{ contact.name }}</h6>
                                        <small class="text-muted">{{ contact.created_at.strftime('%m/%d/%Y') }}</small>
                                    </div>
                                    <p class="mb-1">{{ contact.title }}</p>
                                    <small class="text-muted">{{ contact.company.name }}</small>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Update the JavaScript to work with authenticated user
document.addEventListener('DOMContentLoaded', function() {
    // Add CSRF token to all AJAX requests
    const csrf_token = document.querySelector('meta[name=csrf-token]');
    if (csrf_token) {
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                    xhr.setRequestHeader("X-CSRFToken", csrf_token.getAttribute('content'));
                }
            }
        });
    }
    
    // Your existing JavaScript functionality here
    // (company search, contact search, bulk operations, etc.)
});
</script>
{% endblock %}