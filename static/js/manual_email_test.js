console.log('🚀 Manual Email Test JavaScript Loading...');

// Simplified Manual Email Test Class
class ManualEmailTest {
    constructor() {
        console.log('🏗️ ManualEmailTest constructor called');
        this.manualEmails = [];
        this.selectedRecipients = [];
        this.stats = {
            totalEmails: 0,
            validEmails: 0,
            duplicateAttempts: 0,
            errorCount: 0
        };
        this.init();
    }

    init() {
        console.log('🎯 Initializing ManualEmailTest...');
        this.bindEvents();
        this.logToConsole('✅ Manual Email Test initialized', 'success');
    }

    bindEvents() {
        console.log('🔗 Binding events...');
        
        // Event delegation for better reliability
        document.addEventListener('click', (e) => {
            if (e.target && e.target.id === 'addManualEmailBtn') {
                e.preventDefault();
                console.log('🔥 Add manual email button clicked!');
                this.handleAddManualEmail();
            }
            
            if (e.target && e.target.id === 'testManualEmailBtn') {
                e.preventDefault();
                console.log('🧪 Test button clicked!');
                this.testAddEmail();
            }
        });

        // Enter key handler
        document.addEventListener('keypress', (e) => {
            if (e.target && e.target.id === 'manualEmailAddress' && e.key === 'Enter') {
                e.preventDefault();
                console.log('⌨️ Enter key pressed in email input!');
                this.handleAddManualEmail();
            }
        });
    }

    handleAddManualEmail() {
        this.logToConsole('🚀 handleAddManualEmail called', 'info');
        
        try {
            const emailInput = document.getElementById('manualEmailAddress');
            const nameInput = document.getElementById('manualContactName');
            const titleInput = document.getElementById('manualContactTitle');
            
            if (!emailInput || !nameInput) {
                this.logToConsole('❌ Required input elements not found', 'error');
                this.stats.errorCount++;
                this.updateStats();
                return;
            }
            
            const email = emailInput.value.trim();
            const name = nameInput.value.trim() || 'Manual Contact';
            const title = titleInput ? titleInput.value.trim() || 'Manual Entry' : 'Manual Entry';
            
            this.logToConsole(`📝 Input values: ${email}, ${name}, ${title}`, 'info');
            
            if (!email) {
                this.logToConsole('⚠️ No email entered', 'warning');
                this.showToast('Please enter an email address', 'error');
                return;
            }
            
            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                this.logToConsole('⚠️ Invalid email format', 'warning');
                this.showToast('Please enter a valid email address', 'error');
                this.stats.errorCount++;
                this.updateStats();
                return;
            }
            
            // Check for duplicates
            const existsInManual = this.manualEmails.find(e => e.email === email);
            const existsInSelected = this.selectedRecipients.find(e => e.email === email);
            
            if (existsInManual || existsInSelected) {
                this.logToConsole('⚠️ Email already exists', 'warning');
                this.showToast('This email address is already added', 'error');
                this.stats.duplicateAttempts++;
                this.updateStats();
                return;
            }
            
            // Create email object
            const manualEmail = {
                email: email,
                name: name,
                title: title,
                source: 'manual',
                addedAt: new Date().toISOString()
            };
            
            // Add to arrays
            this.manualEmails.push(manualEmail);
            this.selectedRecipients.push(manualEmail);
            
            this.logToConsole(`✅ Added: ${name} (${email})`, 'success');
            
            // Update stats
            this.stats.totalEmails++;
            this.stats.validEmails++;
            
            // Clear inputs
            emailInput.value = '';
            nameInput.value = '';
            if (titleInput) titleInput.value = '';
            
            // Update displays
            this.updateManualEmailList();
            this.updateSelectedRecipients();
            this.updateStats();
            
            this.showToast(`${name} (${email}) added successfully`, 'success');
            
        } catch (error) {
            this.logToConsole(`💥 Error: ${error.message}`, 'error');
            this.stats.errorCount++;
            this.updateStats();
        }
    }

    updateManualEmailList() {
        const container = document.getElementById('manualEmailContainer');
        if (!container) return;
        
        if (this.manualEmails.length === 0) {
            container.innerHTML = `
                <div class="text-muted text-center py-3">
                    <i class="bi bi-inbox display-6"></i>
                    <p class="mb-0">No emails added yet</p>
                </div>
            `;
            return;
        }
        
        let html = '';
        this.manualEmails.forEach((email, index) => {
            html += `
                <div class="card mb-2">
                    <div class="card-body py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${email.name}</strong>
                                <br>
                                <small class="text-muted">${email.title}</small>
                                <br>
                                <code>${email.email}</code>
                            </div>
                            <button class="btn btn-sm btn-outline-danger" onclick="testApp.removeManualEmail(${index})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }

    updateSelectedRecipients() {
        const container = document.getElementById('selectedRecipients');
        if (!container) return;
        
        if (this.selectedRecipients.length === 0) {
            container.innerHTML = `
                <div class="text-muted text-center py-3">
                    <i class="bi bi-person-plus display-6"></i>
                    <p class="mb-0">No recipients selected</p>
                    <small>Add emails using the form on the left</small>
                </div>
            `;
            return;
        }
        
        let html = '';
        this.selectedRecipients.forEach((recipient, index) => {
            html += `
                <div class="card mb-2 border-primary">
                    <div class="card-body py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${recipient.name}</strong>
                                <i class="bi bi-pencil-square text-success ms-1" title="Manual Entry"></i>
                                <br>
                                <small class="text-muted">${recipient.title}</small>
                                <br>
                                <code>${recipient.email}</code>
                            </div>
                            <button class="btn btn-sm btn-outline-danger" onclick="testApp.removeRecipient(${index})">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }

    removeManualEmail(index) {
        const removed = this.manualEmails.splice(index, 1)[0];
        
        // Also remove from selected recipients
        const recipientIndex = this.selectedRecipients.findIndex(r => r.email === removed.email);
        if (recipientIndex !== -1) {
            this.selectedRecipients.splice(recipientIndex, 1);
        }
        
        this.logToConsole(`🗑️ Removed: ${removed.name} (${removed.email})`, 'warning');
        this.updateManualEmailList();
        this.updateSelectedRecipients();
        this.updateStats();
        this.showToast(`${removed.name} removed`, 'info');
    }

    removeRecipient(index) {
        const removed = this.selectedRecipients.splice(index, 1)[0];
        
        // Also remove from manual emails if it exists there
        const manualIndex = this.manualEmails.findIndex(e => e.email === removed.email);
        if (manualIndex !== -1) {
            this.manualEmails.splice(manualIndex, 1);
        }
        
        this.logToConsole(`🗑️ Removed recipient: ${removed.name}`, 'warning');
        this.updateManualEmailList();
        this.updateSelectedRecipients();
        this.updateStats();
        this.showToast(`${removed.name} removed from recipients`, 'info');
    }

    updateStats() {
        // Update the statistics display
        const elements = {
            totalEmails: document.getElementById('totalEmails'),
            validEmails: document.getElementById('validEmails'),
            duplicateAttempts: document.getElementById('duplicateAttempts'),
            errorCount: document.getElementById('errorCount')
        };
        
        for (const [key, element] of Object.entries(elements)) {
            if (element) {
                element.textContent = this.stats[key];
            }
        }
    }

    showToast(message, type = 'info') {
        // Use the existing toast system from base template
        let toastId;
        switch(type) {
            case 'error': toastId = 'errorToast'; break;
            case 'success': toastId = 'successToast'; break;
            case 'info':
            default: toastId = 'infoToast'; break;
        }
        
        const toastElement = document.getElementById(toastId);
        if (toastElement) {
            const toastBody = toastElement.querySelector('.toast-body');
            if (toastBody) {
                toastBody.textContent = message;
                const toast = new bootstrap.Toast(toastElement);
                toast.show();
            }
        }
    }

    logToConsole(message, type = 'info') {
        // Also log to browser console
        console.log(message);
        
        // Log to the page console if the function exists
        if (typeof logToConsole === 'function') {
            logToConsole(message, type);
        }
    }

    clearAll() {
        this.manualEmails = [];
        this.selectedRecipients = [];
        this.stats = { totalEmails: 0, validEmails: 0, duplicateAttempts: 0, errorCount: 0 };
        
        this.updateManualEmailList();
        this.updateSelectedRecipients();
        this.updateStats();
        
        // Clear form
        const form = document.getElementById('manualEmailForm');
        if (form) form.reset();
        
        this.logToConsole('🗑️ All data cleared', 'warning');
    }

    testAddEmail() {
        this.logToConsole('🧪 Running test add email...', 'info');
        
        const emailInput = document.getElementById('manualEmailAddress');
        const nameInput = document.getElementById('manualContactName');
        
        if (emailInput && nameInput) {
            emailInput.value = `test${Date.now()}@example.com`;
            nameInput.value = 'Test User';
            this.logToConsole('📝 Filled test data', 'info');
            this.handleAddManualEmail();
        } else {
            this.logToConsole('❌ Input elements not found', 'error');
        }
    }
}

// Initialize the test app
console.log('🎯 Initializing ManualEmailTest...');
const testApp = new ManualEmailTest();

// Make it globally available for the main app compatibility
window.app = testApp;

console.log('✅ ManualEmailTest initialized successfully!');
console.log('🧪 Test app available as:', typeof testApp);
