{% extends "base.html" %}

{% block title %}SMTP Settings - JobSearch Pro{% endblock %}

{% block content %}
<div class="container mt-5 pt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('settings') }}">Settings</a></li>
                    <li class="breadcrumb-item active">SMTP Configuration</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="modern-card">
                <div class="card-header-modern">
                    <div class="card-icon bg-info">
                        <i class="bi bi-envelope-gear"></i>
                    </div>
                    <h4 class="mb-0">Add SMTP Configuration</h4>
                </div>
                <div class="card-body-modern">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    {{ form.name(class="form-control") }}
                                    {{ form.name.label(class="form-label") }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.name.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    {{ form.email(class="form-control") }}
                                    {{ form.email.label(class="form-label") }}
                                    {% if form.email.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.email.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row g-3 mt-3">
                            <div class="col-md-8">
                                <div class="form-floating">
                                    {{ form.smtp_server(class="form-control") }}
                                    {{ form.smtp_server.label(class="form-label") }}
                                    {% if form.smtp_server.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.smtp_server.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating">
                                    {{ form.smtp_port(class="form-control") }}
                                    {{ form.smtp_port.label(class="form-label") }}
                                    {% if form.smtp_port.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.smtp_port.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-floating mt-3">
                            {{ form.password(class="form-control") }}
                            {{ form.password.label(class="form-label") }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-check mt-3">
                            {{ form.use_tls(class="form-check-input") }}
                            {{ form.use_tls.label(class="form-check-label") }}
                        </div>
                        
                        <div class="form-check mt-3">
                            {{ form.is_default(class="form-check-input") }}
                            {{ form.is_default.label(class="form-check-label") }}
                        </div>
                        
                        <div class="d-grid mt-4">
                            {{ form.submit(class="btn btn-info btn-lg") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="modern-card">
                <div class="card-header-modern">
                    <div class="card-icon bg-primary">
                        <i class="bi bi-info-circle"></i>
                    </div>
                    <h5 class="mb-0">Common SMTP Settings</h5>
                </div>
                <div class="card-body-modern">
                    <div class="mb-4">
                        <h6 class="text-primary">Gmail</h6>
                        <p class="small mb-1"><strong>Server:</strong> smtp.gmail.com</p>
                        <p class="small mb-1"><strong>Port:</strong> 587</p>
                        <p class="small mb-1"><strong>TLS:</strong> Yes</p>
                        <p class="small text-muted">Use App Password instead of regular password</p>
                    </div>
                    
                    <div class="mb-4">
                        <h6 class="text-primary">Outlook/Hotmail</h6>
                        <p class="small mb-1"><strong>Server:</strong> smtp-mail.outlook.com</p>
                        <p class="small mb-1"><strong>Port:</strong> 587</p>
                        <p class="small mb-1"><strong>TLS:</strong> Yes</p>
                    </div>
                    
                    <div class="mb-4">
                        <h6 class="text-primary">Yahoo</h6>
                        <p class="small mb-1"><strong>Server:</strong> smtp.mail.yahoo.com</p>
                        <p class="small mb-1"><strong>Port:</strong> 587</p>
                        <p class="small mb-1"><strong>TLS:</strong> Yes</p>
                        <p class="small text-muted">Use App Password</p>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="bi bi-shield-exclamation me-2"></i>
                        <small><strong>Security Note:</strong> Your passwords are stored securely and only used for sending emails on your behalf.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Existing Configurations -->
    {% if configs %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="modern-card">
                <div class="card-header-modern">
                    <div class="card-icon bg-success">
                        <i class="bi bi-list"></i>
                    </div>
                    <h5 class="mb-0">Your SMTP Configurations</h5>
                </div>
                <div class="card-body-modern">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Server</th>
                                    <th>Port</th>
                                    <th>TLS</th>
                                    <th>Default</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for config in configs %}
                                <tr>
                                    <td><strong>{{ config.name }}</strong></td>
                                    <td>{{ config.email }}</td>
                                    <td>{{ config.smtp_server }}</td>
                                    <td>{{ config.smtp_port }}</td>
                                    <td>
                                        {% if config.use_tls %}
                                            <span class="badge bg-success">Yes</span>
                                        {% else %}
                                            <span class="badge bg-secondary">No</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if config.is_default %}
                                            <span class="badge bg-primary">Default</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <form method="POST" action="{{ url_for('delete_smtp_config', config_id=config.id) }}" class="d-inline">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                            <button type="submit" class="btn btn-outline-danger btn-sm"
                                                    onclick="return confirm('Are you sure you want to delete this SMTP configuration?')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}