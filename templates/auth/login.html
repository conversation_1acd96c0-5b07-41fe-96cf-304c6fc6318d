{% extends "base.html" %}

{% block title %}Login - JobSearch Pro{% endblock %}

{% block content %}
<div class="container mt-5 pt-4">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="modern-card">
                <div class="card-header-modern text-center">
                    <div class="card-icon bg-primary mx-auto mb-3">
                        <i class="bi bi-box-arrow-in-right"></i>
                    </div>
                    <h3 class="mb-0">Welcome Back!</h3>
                    <p class="text-muted">Sign in to your account</p>
                </div>
                <div class="card-body-modern">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="form-floating mb-3">
                            {{ form.email(class="form-control") }}
                            {{ form.email.label }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-floating mb-3">
                            {{ form.password(class="form-control") }}
                            {{ form.password.label }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-check mb-4">
                            {{ form.remember_me(class="form-check-input") }}
                            {{ form.remember_me.label(class="form-check-label") }}
                        </div>
                        
                        <div class="d-grid">
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-0">Don't have an account? 
                            <a href="{{ url_for('register') }}" class="text-decoration-none">
                                <strong>Sign up here</strong>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}