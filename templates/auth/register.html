{% extends "base.html" %}

{% block title %}Register - JobSearch Pro{% endblock %}

{% block content %}
<div class="container mt-5 pt-4">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="modern-card">
                <div class="card-header-modern text-center">
                    <div class="card-icon bg-success mx-auto mb-3">
                        <i class="bi bi-person-plus"></i>
                    </div>
                    <h3 class="mb-0">Join JobSearch Pro</h3>
                    <p class="text-muted">Create your account to get started</p>
                </div>
                <div class="card-body-modern">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="form-floating mb-3">
                            {{ form.username(class="form-control") }}
                            {{ form.username.label }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-floating mb-3">
                            {{ form.email(class="form-control") }}
                            {{ form.email.label }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-floating mb-3">
                            {{ form.password(class="form-control") }}
                            {{ form.password.label }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-floating mb-4">
                            {{ form.password2(class="form-control") }}
                            {{ form.password2.label }}
                            {% if form.password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password2.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid">
                            {{ form.submit(class="btn btn-success btn-lg") }}
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-0">Already have an account? 
                            <a href="{{ url_for('login') }}" class="text-decoration-none">
                                <strong>Sign in here</strong>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}