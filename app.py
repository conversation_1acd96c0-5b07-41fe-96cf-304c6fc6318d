from flask import Flask, render_template, request, jsonify, flash, redirect, url_for
import requests
from bs4 import BeautifulSoup
import re
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import dns.resolver
import socket
from urllib.parse import urljoin, urlparse
import time
import json
import os
import logging
from dotenv import load_dotenv
from serpapi import GoogleSearch
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
from flask_wtf.csrf import CSRFProtect
from models import db, User, Company, Contact, EmailTemplate, SMTPConfig, EmailSent
from forms import RegistrationForm, LoginForm, SMTPConfigForm, EmailTemplateForm

load_dotenv()

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'your-secret-key-here-change-in-production')
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///jobsearch.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['WTF_CSRF_ENABLED'] = True

# Initialize extensions
db.init_app(app)
csrf = CSRFProtect(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'
login_manager.login_message_category = 'info'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# SerpAPI configuration
SERPAPI_KEY = os.environ.get('SERPAPI_KEY', 'c1824007c8245f45a13212bad16a4d86581f35eb63b6842cb7eaee14e44bd8d2')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create tables
with app.app_context():
    db.create_all()

@app.route('/')
def index():
    if current_user.is_authenticated:
        return render_template('dashboard.html')
    return render_template('index.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(username=form.username.data, email=form.email.data)
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()
        
        # Create default email template for new user
        default_template = EmailTemplate(
            name='Default Job Application',
            subject='Application for Opportunities at {company_name}',
            body='''
            <html>
            <body>
                <p>Dear {name},</p>
                
                <p>I hope this email finds you well. I am writing to express my interest in potential opportunities at your organization.</p>
                
                <p>I am a passionate professional with expertise in my field and would welcome the opportunity to contribute to your team. I believe my skills and experience would be valuable to your organization.</p>
                
                <p>I have attached my resume for your review and would be happy to discuss how I can contribute to your company's success.</p>
                
                <p>Thank you for your time and consideration. I look forward to hearing from you.</p>
                
                <p>Best regards,<br>
                [Your Name]<br>
                [Your Email]<br>
                [Your Phone]</p>
            </body>
            </html>
            ''',
            user_id=user.id,
            is_default=True
        )
        db.session.add(default_template)
        db.session.commit()
        
        flash('Congratulations, you are now registered!', 'success')
        return redirect(url_for('login'))
    
    return render_template('auth/register.html', title='Register', form=form)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        if user and user.check_password(form.password.data):
            login_user(user, remember=form.remember_me.data)
            next_page = request.args.get('next')
            if not next_page:
                next_page = url_for('index')
            flash('Login successful!', 'success')
            return redirect(next_page)
        flash('Invalid email or password', 'danger')
    
    return render_template('auth/login.html', title='Sign In', form=form)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get user's recent activity
    recent_companies = Company.query.filter_by(user_id=current_user.id).order_by(Company.created_at.desc()).limit(5).all()
    recent_contacts = Contact.query.filter_by(user_id=current_user.id).order_by(Contact.created_at.desc()).limit(10).all()
    smtp_configs = SMTPConfig.query.filter_by(user_id=current_user.id).all()
    email_templates = EmailTemplate.query.filter_by(user_id=current_user.id).all()
    
    return render_template('dashboard.html',
                         recent_companies=recent_companies,
                         recent_contacts=recent_contacts,
                         smtp_configs=smtp_configs,
                         email_templates=email_templates)

@app.route('/manual-email-test')
@login_required
def manual_email_test():
    """Dedicated test page for manual email entry functionality"""
    return render_template('manual_email_test.html')

@app.route('/search_company', methods=['POST'])
@login_required
def search_company():
    company_name = request.form.get('company_name')
    if not company_name:
        return jsonify({'error': 'Company name is required'}), 400
    
    try:
        # Search for company domain
        domain_info = search_company_domain(company_name)
        
        # Save to database with user association
        company = Company(
            name=company_name,
            domain=domain_info.get('domain'),
            website=domain_info.get('website'),
            user_id=current_user.id
        )
        db.session.add(company)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'company_id': company.id,
            'domain': domain_info.get('domain'),
            'website': domain_info.get('website')
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/search_contacts', methods=['POST'])
@login_required
def search_contacts():
    company_id = request.form.get('company_id')
    company_name = request.form.get('company_name')
    
    if not company_id or not company_name:
        return jsonify({'error': 'Company ID and name are required'}), 400
    
    try:
        # Verify company belongs to current user
        company = Company.query.filter_by(id=company_id, user_id=current_user.id).first()
        if not company:
            return jsonify({'error': 'Company not found or access denied'}), 403
        
        # Search for HR and Talent Acquisition contacts
        contacts = search_hr_contacts(company_name)
        
        saved_contacts = []
        for contact_data in contacts:
            # Generate possible emails
            emails = generate_email_patterns(contact_data['name'], company_name, company.domain)
            
            contact = Contact(
                name=contact_data['name'],
                title=contact_data['title'],
                company_id=company_id,
                user_id=current_user.id,
                linkedin_url=contact_data.get('linkedin_url'),
                generated_emails=json.dumps(emails)
            )
            db.session.add(contact)
            saved_contacts.append({
                'name': contact_data['name'],
                'title': contact_data['title'],
                'emails': emails,
                'linkedin_url': contact_data.get('linkedin_url')
            })
        
        db.session.commit()
        
        # Log search summary
        logger.info(f"User {current_user.username} found {len(saved_contacts)} HR contacts for {company_name}")
        
        return jsonify({
            'success': True,
            'contacts': saved_contacts,
            'search_summary': {
                'total_contacts': len(saved_contacts),
                'company_name': company_name,
                'has_emails': any(contact.get('emails') for contact in saved_contacts),
                'has_linkedin_profiles': any(contact.get('linkedin_url') for contact in saved_contacts)
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500



@app.route('/bulk_search_and_email', methods=['POST'])
@login_required
def bulk_search_and_email():
    """Search for India-based HR contacts and send emails to all collected email addresses"""
    try:
        data = request.get_json()
        company_name = data.get('company_name')
        smtp_config_id = data.get('smtp_config_id')
        template_id = data.get('template_id')
        
        if not company_name:
            return jsonify({'error': 'Company name is required'}), 400
        
        # Verify SMTP config and template belong to current user
        smtp_config = None
        template = None
        
        if smtp_config_id:
            smtp_config = SMTPConfig.query.filter_by(id=smtp_config_id, user_id=current_user.id).first()
            if not smtp_config:
                return jsonify({'error': 'SMTP configuration not found or access denied'}), 403
        
        if template_id:
            template = EmailTemplate.query.filter_by(id=template_id, user_id=current_user.id).first()
            if not template:
                return jsonify({'error': 'Email template not found or access denied'}), 403
        
        # Search for company domain
        domain_info = search_company_domain(company_name)
        
        # Save company to database with user association
        company = Company(
            name=company_name,
            domain=domain_info.get('domain'),
            website=domain_info.get('website'),
            user_id=current_user.id
        )
        db.session.add(company)
        db.session.commit()
        
        # Search for India-based HR contacts
        contacts = search_hr_contacts(company_name)
        
        # Collect all emails
        all_emails = []
        saved_contacts = []
        
        for contact_data in contacts:
            # Generate possible emails
            emails = generate_email_patterns(contact_data['name'], company_name, domain_info.get('domain'))
            
            # Save contact to database
            contact = Contact(
                name=contact_data['name'],
                title=contact_data['title'],
                company_id=company.id,
                user_id=current_user.id,
                linkedin_url=contact_data.get('linkedin_url'),
                generated_emails=json.dumps(emails)
            )
            db.session.add(contact)
            
            # Add direct email if available
            if contact_data.get('email'):
                all_emails.append({
                    'email': contact_data['email'],
                    'name': contact_data['name'],
                    'title': contact_data['title'],
                    'source': 'direct'
                })
            
            # Add generated emails
            for email in emails:
                all_emails.append({
                    'email': email,
                    'name': contact_data['name'],
                    'title': contact_data['title'],
                    'source': 'generated'
                })
            
            saved_contacts.append({
                'name': contact_data['name'],
                'title': contact_data['title'],
                'emails': emails,
                'direct_email': contact_data.get('email'),
                'linkedin_url': contact_data.get('linkedin_url')
            })
        
        db.session.commit()
        
        # Remove duplicate emails
        unique_emails = []
        seen_emails = set()
        for email_data in all_emails:
            if email_data['email'] not in seen_emails:
                seen_emails.add(email_data['email'])
                unique_emails.append(email_data)
        
        # Send emails if SMTP config and template are provided
        email_results = []
        if smtp_config_id and template_id:
            smtp_config = SMTPConfig.query.get(smtp_config_id)
            template = EmailTemplate.query.get(template_id)
            
            if smtp_config and template:
                for email_data in unique_emails:
                    result = send_email(
                        smtp_config, 
                        template, 
                        email_data['email'], 
                        email_data['name']
                    )
                    email_results.append({
                        'email': email_data['email'],
                        'name': email_data['name'],
                        'title': email_data['title'],
                        'source': email_data['source'],
                        'success': result['success'],
                        'message': result['message']
                    })
                    
                    # Add small delay between emails to avoid rate limiting
                    time.sleep(1)
        
        return jsonify({
            'success': True,
            'company_info': {
                'name': company_name,
                'domain': domain_info.get('domain'),
                'website': domain_info.get('website')
            },
            'contacts_found': len(saved_contacts),
            'total_emails_collected': len(unique_emails),
            'emails_sent': len(email_results),
            'contacts': saved_contacts,
            'all_emails': unique_emails,
            'email_results': email_results
        })
        
    except Exception as e:
        logger.error(f"Error in bulk search and email: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/send_emails', methods=['POST'])
@login_required
def send_emails():
    try:
        data = request.get_json()
        smtp_config_id = data.get('smtp_config_id')
        template_id = data.get('template_id')
        recipients = data.get('recipients', [])
        
        smtp_config = SMTPConfig.query.filter_by(id=smtp_config_id, user_id=current_user.id).first()
        template = EmailTemplate.query.filter_by(id=template_id, user_id=current_user.id).first()
        
        if not smtp_config or not template:
            return jsonify({'error': 'SMTP configuration or template not found or access denied'}), 403
        
        results = []
        for recipient in recipients:
            result = send_email(
                smtp_config, 
                template, 
                recipient['email'], 
                recipient.get('name', 'HR Professional')
            )
            results.append({
                'email': recipient['email'],
                'success': result['success'],
                'message': result['message']
            })
            
            # Add delay between emails
            time.sleep(1)
        
        return jsonify({
            'success': True,
            'results': results
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/smtp_configs')
@login_required
def smtp_configs():
    configs = SMTPConfig.query.filter_by(user_id=current_user.id).all()
    return jsonify([{
        'id': config.id,
        'name': config.name,
        'email': config.email,
        'smtp_server': config.smtp_server,
        'is_default': config.is_default
    } for config in configs])

@app.route('/email_templates')
@login_required
def email_templates():
    templates = EmailTemplate.query.filter_by(user_id=current_user.id).all()
    return jsonify([{
        'id': template.id,
        'name': template.name,
        'subject': template.subject,
        'body': template.body,
        'is_default': template.is_default
    } for template in templates])

@app.route('/add_smtp_config', methods=['POST'])
@login_required
def add_smtp_config():
    try:
        data = request.get_json()
        config = SMTPConfig(
            name=data['name'],
            smtp_server=data['smtp_server'],
            smtp_port=data['smtp_port'],
            email=data['email'],
            password=data['password'],
            use_tls=data.get('use_tls', True),
            user_id=current_user.id
        )
        db.session.add(config)
        db.session.commit()
        
        return jsonify({'success': True, 'config_id': config.id})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/add_email_template', methods=['POST'])
@login_required
def add_email_template():
    try:
        data = request.get_json()
        template = EmailTemplate(
            name=data['name'],
            subject=data['subject'],
            body=data['body'],
            user_id=current_user.id
        )
        db.session.add(template)
        db.session.commit()
        
        return jsonify({'success': True, 'template_id': template.id})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/settings')
@login_required
def settings():
    smtp_configs = SMTPConfig.query.filter_by(user_id=current_user.id).all()
    email_templates = EmailTemplate.query.filter_by(user_id=current_user.id).all()
    return render_template('settings.html', 
                         smtp_configs=smtp_configs, 
                         email_templates=email_templates)

@app.route('/settings/smtp', methods=['GET', 'POST'])
@login_required
def smtp_settings():
    form = SMTPConfigForm()
    if form.validate_on_submit():
        # Set all existing configs to non-default if this is being set as default
        if form.is_default.data:
            SMTPConfig.query.filter_by(user_id=current_user.id).update({'is_default': False})
        
        config = SMTPConfig(
            name=form.name.data,
            smtp_server=form.smtp_server.data,
            smtp_port=form.smtp_port.data,
            email=form.email.data,
            password=form.password.data,
            use_tls=form.use_tls.data,
            user_id=current_user.id,
            is_default=form.is_default.data
        )
        db.session.add(config)
        db.session.commit()
        flash('SMTP configuration saved successfully!', 'success')
        return redirect(url_for('settings'))
    
    configs = SMTPConfig.query.filter_by(user_id=current_user.id).all()
    return render_template('settings/smtp.html', form=form, configs=configs)

@app.route('/settings/templates', methods=['GET', 'POST'])
@login_required
def template_settings():
    form = EmailTemplateForm()
    if form.validate_on_submit():
        template = EmailTemplate(
            name=form.name.data,
            subject=form.subject.data,
            body=form.body.data,
            user_id=current_user.id
        )
        db.session.add(template)
        db.session.commit()
        flash('Email template saved successfully!', 'success')
        return redirect(url_for('settings'))
    
    templates = EmailTemplate.query.filter_by(user_id=current_user.id).all()
    return render_template('settings/templates.html', form=form, templates=templates)

@app.route('/delete_smtp_config/<int:config_id>', methods=['POST'])
@login_required
def delete_smtp_config(config_id):
    config = SMTPConfig.query.filter_by(id=config_id, user_id=current_user.id).first()
    if config:
        db.session.delete(config)
        db.session.commit()
        flash('SMTP configuration deleted successfully!', 'success')
    return redirect(url_for('settings'))

@app.route('/delete_email_template/<int:template_id>', methods=['POST'])
@login_required
def delete_email_template(template_id):
    template = EmailTemplate.query.filter_by(id=template_id, user_id=current_user.id).first()
    if template:
        db.session.delete(template)
        db.session.commit()
        flash('Email template deleted successfully!', 'success')
    return redirect(url_for('settings'))

# Utility Functions
def search_company_domain(company_name):
    """Search for company domain using various methods"""
    try:
        # Method 1: Search engines
        search_query = f"{company_name} official website"
        search_url = f"https://www.google.com/search?q={search_query}"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(search_url, headers=headers, timeout=10)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Extract domain from search results
        links = soup.find_all('a', href=True)
        for link in links:
            href = link.get('href')
            if 'url?q=' in href:
                url = href.split('url?q=')[1].split('&')[0]
                if is_company_website(url, company_name):
                    domain = urlparse(url).netloc
                    return {
                        'domain': domain,
                        'website': url
                    }
        
        # Method 2: Try common patterns
        common_patterns = [
            f"{company_name.lower().replace(' ', '')}.com",
            f"{company_name.lower().replace(' ', '')}.co",
            f"{company_name.lower().replace(' ', '')}.org",
            f"{company_name.lower().replace(' ', '')}.net"
        ]
        
        for domain in common_patterns:
            try:
                response = requests.get(f"https://{domain}", timeout=5)
                if response.status_code == 200:
                    return {
                        'domain': domain,
                        'website': f"https://{domain}"
                    }
            except:
                continue
        
        return {'domain': None, 'website': None}
    except Exception as e:
        logger.error(f"Error searching company domain: {e}")
        return {'domain': None, 'website': None}

def is_company_website(url, company_name):
    """Check if URL is likely the company's official website"""
    try:
        response = requests.get(url, timeout=10)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        title = soup.find('title')
        if title and company_name.lower() in title.text.lower():
            return True
        
        # Check meta description
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc and company_name.lower() in meta_desc.get('content', '').lower():
            return True
        
        return False
    except:
        return False

def search_hr_emails(company_name):
    """Search specifically for India-based HR email addresses"""
    hr_emails = []
    
    try:
        # Search queries focused on finding India-based HR email addresses
        email_search_queries = [
            f"{company_name} hr email contact India",
            f"{company_name} human resources email India",
            f"{company_name} careers email contact India",
            f"{company_name} jobs email contact India",
            f"{company_name} India hr email",
            f"{company_name} India careers email",
            f"site:{company_name.lower().replace(' ', '')}.com hr email",
            f"site:{company_name.lower().replace(' ', '')}.com careers email",
            f"site:{company_name.lower().replace(' ', '')}.co.in hr email",
            f"site:{company_name.lower().replace(' ', '')}.co.in careers email"
        ]
        
        for query in email_search_queries:
            try:
                params = {
                    "engine": "google",
                    "q": query,
                    "api_key": SERPAPI_KEY,
                    "num": 5
                }
                
                search = GoogleSearch(params)
                results = search.get_dict()
                
                if "organic_results" in results:
                    for result in results["organic_results"]:
                        snippet = result.get("snippet", "")
                        title = result.get("title", "")
                        
                        # Extract emails from snippets
                        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
                        found_emails = re.findall(email_pattern, f"{title} {snippet}")
                        
                        for email in found_emails:
                            # Check if it's an HR-related email
                            hr_keywords = ["hr@", "human", "career", "job", "talent", "recruit"]
                            if any(keyword in email.lower() for keyword in hr_keywords):
                                hr_emails.append({
                                    'name': 'HR Contact',
                                    'title': 'Human Resources',
                                    'linkedin_url': None,
                                    'email': email,
                                    'source': 'direct_search'
                                })
                
                time.sleep(0.5)  # Rate limiting
                
            except Exception as e:
                logger.error(f"Error in email search query '{query}': {e}")
                continue
                
    except Exception as e:
        logger.error(f"Error searching HR emails: {e}")
    
    # Remove duplicates based on email
    seen = set()
    unique_emails = []
    for contact in hr_emails:
        if contact.get('email') and contact['email'] not in seen:
            seen.add(contact['email'])
            unique_emails.append(contact)
    
    return unique_emails

def search_hr_contacts(company_name):
    """Search for India-based HR and Talent Acquisition contacts using SerpAPI"""
    contacts = []
    
    try:
        # First, try to find direct HR email addresses for India operations
        hr_emails = search_hr_emails(company_name)
        for hr_email in hr_emails:
            contacts.append(hr_email)
        
        # Search for India-based HR professionals at the company
        hr_search_queries = [
            f"{company_name} HR Manager India site:linkedin.com",
            f"{company_name} Human Resources Director India site:linkedin.com",
            f"{company_name} Talent Acquisition India site:linkedin.com",
            f"{company_name} Recruiter India site:linkedin.com",
            f"{company_name} People Operations India site:linkedin.com",
            f"{company_name} HR Manager Mumbai site:linkedin.com",
            f"{company_name} HR Manager Delhi site:linkedin.com",
            f"{company_name} HR Manager Bangalore site:linkedin.com",
            f"{company_name} HR Manager Chennai site:linkedin.com",
            f"{company_name} HR Manager Hyderabad site:linkedin.com",
            f"{company_name} HR Manager Pune site:linkedin.com"
        ]
        
        seen_profiles = set()  # To avoid duplicates
        
        for query in hr_search_queries:
            try:
                params = {
                    "engine": "google",
                    "q": query,
                    "api_key": SERPAPI_KEY,
                    "num": 10  # Number of results per query
                }
                
                search = GoogleSearch(params)
                results = search.get_dict()
                
                if "organic_results" in results:
                    for result in results["organic_results"]:
                        title = result.get("title", "")
                        link = result.get("link", "")
                        snippet = result.get("snippet", "")
                        
                        # Check if it's a LinkedIn profile
                        if "linkedin.com/in/" in link and link not in seen_profiles:
                            seen_profiles.add(link)
                            
                            # Extract name and title from the title or snippet
                            contact_info = extract_contact_info(title, snippet, company_name)
                            if contact_info:
                                contacts.append({
                                    'name': contact_info['name'],
                                    'title': contact_info['title'],
                                    'linkedin_url': link
                                })
                
                # Add a small delay to respect rate limits
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Error in search query '{query}': {e}")
                continue
        
        # If we didn't find any contacts, try a broader search
        if not contacts:
            try:
                params = {
                    "engine": "google",
                    "q": f"{company_name} HR contact email",
                    "api_key": SERPAPI_KEY,
                    "num": 5
                }
                
                search = GoogleSearch(params)
                results = search.get_dict()
                
                if "organic_results" in results:
                    for result in results["organic_results"]:
                        title = result.get("title", "")
                        snippet = result.get("snippet", "")
                        
                        # Look for HR-related information in the results
                        contact_info = extract_hr_info_from_snippet(snippet, company_name)
                        if contact_info:
                            contacts.append(contact_info)
                            
            except Exception as e:
                logger.error(f"Error in broader HR search: {e}")
        
        # If still no results, return some fallback data
        if not contacts:
            contacts = get_fallback_hr_contacts(company_name)
            
    except Exception as e:
        logger.error(f"Error searching HR contacts: {e}")
        # Return fallback data in case of error
        contacts = get_fallback_hr_contacts(company_name)
    
    return contacts[:8]  # Limit to 8 contacts max

def extract_contact_info(title, snippet, company_name):
    """Extract contact information from India-based LinkedIn search results"""
    try:
        # Common HR title patterns
        hr_titles = [
            "Human Resources", "HR Manager", "HR Director", "Talent Acquisition", 
            "Recruiter", "People Operations", "HR Business Partner", "HR Generalist",
            "Chief People Officer", "VP of People", "Head of Talent", "Recruiting Manager",
            "Assistant Manager - HR", "Senior HR Manager", "HR Lead", "Talent Partner"
        ]
        
        # India location indicators
        india_indicators = [
            "india", "mumbai", "delhi", "bangalore", "chennai", "hyderabad", 
            "pune", "kolkata", "gurgaon", "noida", "ahmedabad", "jaipur"
        ]
        
        full_text = f"{title} {snippet}".lower()
        
        # Check if this is an HR-related profile
        is_hr = any(hr_title.lower() in full_text for hr_title in hr_titles)
        is_company = company_name.lower() in full_text
        is_india = any(location in full_text for location in india_indicators)
        
        # Prioritize India-based profiles
        if is_hr and is_company and is_india:
            # Extract name (usually the first part of LinkedIn title)
            name_match = re.match(r'^([A-Z][a-z]+ [A-Z][a-z]+)', title)
            if name_match:
                name = name_match.group(1)
            else:
                # Fallback: try to extract from title patterns
                name_parts = title.split(' - ')[0].split(' | ')[0].strip()
                if len(name_parts.split()) >= 2:
                    name = name_parts
                else:
                    name = "HR Professional"
            
            # Extract title
            for hr_title in hr_titles:
                if hr_title.lower() in full_text:
                    extracted_title = hr_title
                    break
            else:
                extracted_title = "HR Professional"
            
            return {
                'name': name,
                'title': extracted_title,
                'location': 'India'
            }
        # Also accept general HR profiles if no India-specific ones found
        elif is_hr and is_company:
            # Extract name (usually the first part of LinkedIn title)
            name_match = re.match(r'^([A-Z][a-z]+ [A-Z][a-z]+)', title)
            if name_match:
                name = name_match.group(1)
            else:
                # Fallback: try to extract from title patterns
                name_parts = title.split(' - ')[0].split(' | ')[0].strip()
                if len(name_parts.split()) >= 2:
                    name = name_parts
                else:
                    name = "HR Professional"
            
            # Extract title
            for hr_title in hr_titles:
                if hr_title.lower() in full_text:
                    extracted_title = hr_title
                    break
            else:
                extracted_title = "HR Professional"
            
            return {
                'name': name,
                'title': extracted_title,
                'location': 'Unknown'
            }
    except Exception as e:
        logger.error(f"Error extracting contact info: {e}")
    
    return None

def extract_hr_info_from_snippet(snippet, company_name):
    """Extract HR contact info from search snippets"""
    try:
        # Look for email patterns
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, snippet)
        
        # Look for HR-related keywords
        hr_keywords = ["hr@", "human.resources@", "careers@", "jobs@", "talent@", "recruiting@"]
        
        for email in emails:
            if any(keyword in email.lower() for keyword in hr_keywords):
                return {
                    'name': 'HR Department',
                    'title': 'Human Resources',
                    'linkedin_url': None,
                    'email': email
                }
    except Exception as e:
        logger.error(f"Error extracting HR info from snippet: {e}")
    
    return None

def get_fallback_hr_contacts(company_name):
    """Return fallback HR contacts if search fails"""
    return [
        {
            'name': 'HR Department',
            'title': 'Human Resources',
            'linkedin_url': None
        },
        {
            'name': 'Talent Acquisition Team',
            'title': 'Talent Acquisition',
            'linkedin_url': None
        }
    ]

def generate_email_patterns(name, company_name, company_domain=None):
    """Generate possible email patterns for a person"""
    name_parts = name.lower().split()
    first_name = name_parts[0] if name_parts else ""
    last_name = name_parts[-1] if len(name_parts) > 1 else ""
    
    # Use actual company domain if available, otherwise generate one
    if company_domain:
        domain = company_domain
    else:
        domain = f"{company_name.lower().replace(' ', '').replace(',', '').replace('.', '')}.com"
    
    patterns = []
    if first_name and last_name and first_name != "hr" and first_name != "talent":
        patterns = [
            f"{first_name}.{last_name}@{domain}",
            f"{first_name}{last_name}@{domain}",
            f"{first_name[0]}{last_name}@{domain}",
            f"{first_name}.{last_name[0]}@{domain}",
            f"{first_name}_{last_name}@{domain}",
            f"{last_name}.{first_name}@{domain}",
            f"{last_name}{first_name}@{domain}",
            f"{first_name[0]}.{last_name}@{domain}"
        ]
    elif first_name and first_name != "hr" and first_name != "talent":
        patterns = [f"{first_name}@{domain}"]
    else:
        # For generic HR contacts, generate common HR email patterns
        patterns = [
            f"hr@{domain}",
            f"humanresources@{domain}",
            f"careers@{domain}",
            f"jobs@{domain}",
            f"talent@{domain}",
            f"recruiting@{domain}"
        ]
    
    return patterns



def send_email(smtp_config, template, recipient_email, recipient_name):
    """Send email using provided SMTP configuration"""
    try:
        # Create message
        msg = MIMEMultipart()
        msg['From'] = smtp_config.email
        msg['To'] = recipient_email
        msg['Subject'] = template.subject.replace('{name}', recipient_name)
        
        # Personalize email body
        body = template.body.replace('{name}', recipient_name)
        msg.attach(MIMEText(body, 'html'))
        
        # Send email
        server = smtplib.SMTP(smtp_config.smtp_server, smtp_config.smtp_port)
        if smtp_config.use_tls:
            server.starttls()
        server.login(smtp_config.email, smtp_config.password)
        server.send_message(msg)
        server.quit()
        
        return {'success': True, 'message': 'Email sent successfully'}
    except Exception as e:
        return {'success': False, 'message': str(e)}

if __name__ == '__main__':
    app.run(debug=True, port=5004)