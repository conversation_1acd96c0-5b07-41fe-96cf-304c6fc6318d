{% extends "base.html" %}

{% block title %}Settings - JobSearch Pro{% endblock %}

{% block content %}
<div class="container mt-5 pt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="bi bi-gear me-2"></i>Settings
            </h2>
        </div>
    </div>

    <div class="row g-4">
        <!-- SMTP Configurations -->
        <div class="col-lg-6">
            <div class="modern-card">
                <div class="card-header-modern">
                    <div class="card-icon bg-info">
                        <i class="bi bi-envelope-gear"></i>
                    </div>
                    <h5 class="mb-0">SMTP Configurations</h5>
                </div>
                <div class="card-body-modern">
                    {% if smtp_configs %}
                        <div class="list-group list-group-flush mb-3">
                            {% for config in smtp_configs %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">
                                        {{ config.name }}
                                        {% if config.is_default %}
                                            <span class="badge bg-primary ms-2">Default</span>
                                        {% endif %}
                                    </h6>
                                    <small class="text-muted">{{ config.email }} - {{ config.smtp_server }}:{{ config.smtp_port }}</small>
                                </div>
                                <form method="POST" action="{{ url_for('delete_smtp_config', config_id=config.id) }}" class="d-inline">
                                    <button type="submit" class="btn btn-outline-danger btn-sm" 
                                            onclick="return confirm('Are you sure you want to delete this SMTP configuration?')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            No SMTP configurations found. Add your first configuration to start sending emails.
                        </div>
                    {% endif %}
                    
                    <a href="{{ url_for('smtp_settings') }}" class="btn btn-info w-100">
                        <i class="bi bi-plus-circle me-2"></i>
                        Add SMTP Configuration
                    </a>
                </div>
            </div>
        </div>

        <!-- Email Templates -->
        <div class="col-lg-6">
            <div class="modern-card">
                <div class="card-header-modern">
                    <div class="card-icon bg-warning">
                        <i class="bi bi-file-earmark-text"></i>
                    </div>
                    <h5 class="mb-0">Email Templates</h5>
                </div>
                <div class="card-body-modern">
                    {% if email_templates %}
                        <div class="list-group list-group-flush mb-3">
                            {% for template in email_templates %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">
                                        {{ template.name }}
                                        {% if template.is_default %}
                                            <span class="badge bg-primary ms-2">Default</span>
                                        {% endif %}
                                    </h6>
                                    <small class="text-muted">{{ template.subject[:50] }}{% if template.subject|length > 50 %}...{% endif %}</small>
                                </div>
                                <form method="POST" action="{{ url_for('delete_email_template', template_id=template.id) }}" class="d-inline">
                                    <button type="submit" class="btn btn-outline-danger btn-sm" 
                                            onclick="return confirm('Are you sure you want to delete this email template?')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            No email templates found. Add your first template to customize your outreach emails.
                        </div>
                    {% endif %}
                    
                    <a href="{{ url_for('template_settings') }}" class="btn btn-warning w-100">
                        <i class="bi bi-plus-circle me-2"></i>
                        Add Email Template
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="modern-card">
                <div class="card-header-modern">
                    <div class="card-icon bg-success">
                        <i class="bi bi-person-circle"></i>
                    </div>
                    <h5 class="mb-0">Account Information</h5>
                </div>
                <div class="card-body-modern">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">Username</label>
                                <p class="form-control-plaintext">{{ current_user.username }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">Email</label>
                                <p class="form-control-plaintext">{{ current_user.email }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">Member Since</label>
                                <p class="form-control-plaintext">{{ current_user.created_at.strftime('%B %d, %Y') }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-semibold">Status</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-success">Active</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}