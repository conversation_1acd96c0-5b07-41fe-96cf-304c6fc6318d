{% extends "base.html" %}

{% block title %}Manual Email Entry - Test Page{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Manual Email Entry - Test Page</h1>
                    <p class="text-muted">Dedicated testing environment for manual email functionality</p>
                </div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>

            <!-- Debug Information -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Debug Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>JavaScript Status:</h6>
                                    <div id="jsStatus" class="mb-3">
                                        <span class="badge bg-warning">Checking...</span>
                                    </div>
                                    <h6>Elements Status:</h6>
                                    <div id="elementsStatus" class="mb-3">
                                        <span class="badge bg-warning">Checking...</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Test Functions:</h6>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-sm btn-outline-primary" onclick="runDiagnostics()">
                                            <i class="bi bi-gear me-1"></i>Run Diagnostics
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="testAddEmail()">
                                            <i class="bi bi-plus-circle me-1"></i>Test Add Email
                                        </button>
                                        <button class="btn btn-sm btn-outline-info" onclick="clearAll()">
                                            <i class="bi bi-trash me-1"></i>Clear All
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Manual Email Entry Section -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-pencil-square me-2"></i>Manual Email Entry
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="manualEmailForm">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="manualEmailAddress" 
                                           placeholder="Enter email address" required>
                                    <label for="manualEmailAddress">Email Address *</label>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="manualContactName" 
                                           placeholder="Enter contact name">
                                    <label for="manualContactName">Contact Name (Optional)</label>
                                </div>
                                
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="manualContactTitle" 
                                           placeholder="Enter job title">
                                    <label for="manualContactTitle">Job Title (Optional)</label>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-success" id="addManualEmailBtn">
                                        <i class="bi bi-plus-circle me-2"></i>Add Email
                                    </button>
                                    <button type="button" class="btn btn-outline-info" id="testManualEmailBtn">
                                        <i class="bi bi-bug me-2"></i>Test Function
                                    </button>
                                </div>
                            </form>
                            
                            <!-- Manual Email List -->
                            <div id="manualEmailList" class="mt-4">
                                <h6 class="border-bottom pb-2">Added Emails:</h6>
                                <div id="manualEmailContainer">
                                    <div class="text-muted text-center py-3">
                                        <i class="bi bi-inbox display-6"></i>
                                        <p class="mb-0">No emails added yet</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-people me-2"></i>Recipients List
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="selectedRecipients">
                                <div class="text-muted text-center py-3">
                                    <i class="bi bi-person-plus display-6"></i>
                                    <p class="mb-0">No recipients selected</p>
                                    <small>Add emails using the form on the left</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Console Output -->
                    <div class="card mt-4">
                        <div class="card-header bg-dark text-white">
                            <h6 class="mb-0">
                                <i class="bi bi-terminal me-2"></i>Console Output
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            <div id="consoleOutput" class="bg-dark text-light p-3" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.875rem;">
                                <div class="text-success">Console ready...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary" id="totalEmails">0</h3>
                            <p class="mb-0">Total Emails</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success" id="validEmails">0</h3>
                            <p class="mb-0">Valid Emails</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning" id="duplicateAttempts">0</h3>
                            <p class="mb-0">Duplicate Attempts</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-danger" id="errorCount">0</h3>
                            <p class="mb-0">Errors</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Dedicated JavaScript for manual email testing -->
<script src="{{ url_for('static', filename='js/manual_email_test.js') }}"></script>
<script>
// Test page specific functionality
let testStats = {
    totalEmails: 0,
    validEmails: 0,
    duplicateAttempts: 0,
    errorCount: 0
};

// Console output functionality
function logToConsole(message, type = 'info') {
    const consoleOutput = document.getElementById('consoleOutput');
    const timestamp = new Date().toLocaleTimeString();
    const colorClass = {
        'info': 'text-info',
        'success': 'text-success',
        'warning': 'text-warning',
        'error': 'text-danger'
    }[type] || 'text-light';
    
    const logEntry = document.createElement('div');
    logEntry.className = colorClass;
    logEntry.innerHTML = `[${timestamp}] ${message}`;
    
    consoleOutput.appendChild(logEntry);
    consoleOutput.scrollTop = consoleOutput.scrollHeight;
}

// Update statistics
function updateStats() {
    document.getElementById('totalEmails').textContent = testStats.totalEmails;
    document.getElementById('validEmails').textContent = testStats.validEmails;
    document.getElementById('duplicateAttempts').textContent = testStats.duplicateAttempts;
    document.getElementById('errorCount').textContent = testStats.errorCount;
}

// Test functions
function runDiagnostics() {
    logToConsole('🔍 Running diagnostics...', 'info');
    
    // Check if app exists
    if (typeof app !== 'undefined') {
        logToConsole('✅ App instance found', 'success');
        document.getElementById('jsStatus').innerHTML = '<span class="badge bg-success">Ready</span>';
    } else {
        logToConsole('❌ App instance not found', 'error');
        document.getElementById('jsStatus').innerHTML = '<span class="badge bg-danger">Error</span>';
        testStats.errorCount++;
    }
    
    // Check elements
    const elements = {
        emailInput: document.getElementById('manualEmailAddress'),
        nameInput: document.getElementById('manualContactName'),
        addBtn: document.getElementById('addManualEmailBtn')
    };
    
    let allElementsFound = true;
    for (const [name, element] of Object.entries(elements)) {
        if (element) {
            logToConsole(`✅ ${name} found`, 'success');
        } else {
            logToConsole(`❌ ${name} not found`, 'error');
            allElementsFound = false;
            testStats.errorCount++;
        }
    }
    
    if (allElementsFound) {
        document.getElementById('elementsStatus').innerHTML = '<span class="badge bg-success">All Found</span>';
    } else {
        document.getElementById('elementsStatus').innerHTML = '<span class="badge bg-danger">Missing Elements</span>';
    }
    
    updateStats();
}

function testAddEmail() {
    logToConsole('🧪 Testing add email functionality...', 'info');
    
    const emailInput = document.getElementById('manualEmailAddress');
    const nameInput = document.getElementById('manualContactName');
    
    if (emailInput && nameInput) {
        emailInput.value = `test${Date.now()}@example.com`;
        nameInput.value = 'Test User';
        logToConsole('📝 Filled test data', 'info');
        
        if (typeof app !== 'undefined' && app.handleAddManualEmail) {
            app.handleAddManualEmail();
            testStats.totalEmails++;
        } else {
            logToConsole('❌ handleAddManualEmail function not available', 'error');
            testStats.errorCount++;
        }
    } else {
        logToConsole('❌ Input elements not found', 'error');
        testStats.errorCount++;
    }
    
    updateStats();
}

function clearAll() {
    logToConsole('🗑️ Clearing all data...', 'warning');
    
    if (typeof app !== 'undefined') {
        app.manualEmails = [];
        app.selectedRecipients = [];
        app.updateManualEmailList();
        app.updateSelectedRecipients();
    }
    
    // Reset form
    document.getElementById('manualEmailForm').reset();
    
    // Reset stats
    testStats = { totalEmails: 0, validEmails: 0, duplicateAttempts: 0, errorCount: 0 };
    updateStats();
    
    logToConsole('✅ All data cleared', 'success');
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    logToConsole('🚀 Manual Email Test Page loaded', 'success');
    
    // Run initial diagnostics
    setTimeout(runDiagnostics, 1000);
    
    // Update stats display
    updateStats();
});
</script>
{% endblock %}
